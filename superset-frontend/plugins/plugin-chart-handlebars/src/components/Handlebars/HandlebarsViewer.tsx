// DODO was here
import {
  SafeMarkdown,
  styled,
  t,
  FeatureFlag,
  isFeatureEnabled,
  createSmartNumberFormatter,
} from '@superset-ui/core';
import Handlebars from 'handlebars';
import moment from 'moment';
import { useMemo, useState, useRef, useEffect } from 'react';
import { isPlainObject } from 'lodash';
import Helpers from 'just-handlebars-helpers';
import Navigable from '../../DodoExtensions/components/Navigable'; // DODO added 49751291
import sandboxedEval from '../../utils/sandbox'; // DODO added 49751291

interface HandlebarsViewerPropsDodoExtended {
  allowNavigationTools?: boolean; // DODO added 49751291
  jsExecuteCode?: string; // DODO added 49751291
}
export interface HandlebarsViewerProps
  extends HandlebarsViewerPropsDodoExtended {
  templateSource: string;
  data: any;
}

// DODO added 49751291
const ErrorStyled = styled.pre`
  white-space: pre-wrap;
`;

export const HandlebarsViewer = ({
  templateSource,
  data,
  allowNavigationTools = false, // DODO added 49751291
  jsExecuteCode, // DODO added 49751291
}: HandlebarsViewerProps) => {
  const [renderedTemplate, setRenderedTemplate] = useState('');
  const [error, setError] = useState('');
  const containerRef = useRef<HTMLDivElement>(null); // DODO added 49751291
  const appContainer = document.getElementById('app');
  const { common } = JSON.parse(
    appContainer?.getAttribute('data-bootstrap') || '{}',
  );
  // DODO changed 44611022
  const htmlSanitization =
    common?.conf?.HTML_SANITIZATION ?? window.htmlSanitization ?? true;
  const htmlSchemaOverrides =
    common?.conf?.HTML_SANITIZATION_SCHEMA_EXTENSIONS || {};

  useMemo(() => {
    try {
      const template = Handlebars.compile(templateSource);
      const result = template(data);
      setRenderedTemplate(result);
      setError('');
    } catch (error) {
      setRenderedTemplate('');
      setError((error as Error).message);
    }
  }, [templateSource, data]);

  // DODO added 49751291
  // Execute JavaScript code after the template is rendered
  useEffect(() => {
    if (
      containerRef.current &&
      renderedTemplate &&
      jsExecuteCode &&
      isFeatureEnabled(FeatureFlag.EnableJavascriptControls)
    ) {
      try {
        const jsFunction = sandboxedEval(jsExecuteCode);
        if (typeof jsFunction === 'function') {
          jsFunction(containerRef.current, data);
        } else {
          console.warn('JavaScript execute code must be a function');
        }
      } catch (error) {
        console.warn('Error executing JavaScript code:', error);
      }
    }
  }, [renderedTemplate, jsExecuteCode, data]);

  if (error) {
    return <ErrorStyled>{error}</ErrorStyled>; // DODO changed 49751291
  }

  if (renderedTemplate) {
    // DODO changed start 49751291
    // Create a wrapper div with a ref to access the DOM
    const content = (
      <div ref={containerRef}>
        <SafeMarkdown
          source={renderedTemplate}
          htmlSanitization={htmlSanitization}
          htmlSchemaOverrides={htmlSchemaOverrides}
        />
      </div>
    );

    return allowNavigationTools ? <Navigable>{content}</Navigable> : content;
    // DODO changed stop 49751291
  }
  return <p>{t('Loading...')}</p>;
};

//  usage: {{dateFormat my_date format="MMMM YYYY"}}
Handlebars.registerHelper('dateFormat', function (context, block) {
  const f = block.hash.format || 'YYYY-MM-DD';
  return moment(context).format(f);
});

// usage: {{  }}
Handlebars.registerHelper('stringify', (obj: any, obj2: any) => {
  // calling without an argument
  if (obj2 === undefined)
    throw Error('Please call with an object. Example: `stringify myObj`');
  return isPlainObject(obj) ? JSON.stringify(obj) : String(obj);
});

// DODO added start 56601134
Handlebars.registerHelper('round', (num, precision = 0) =>
  Number(num).toFixed(precision),
);

Handlebars.registerHelper(
  'numberFormat',
  (
    value,
    options: {
      hash: {
        decimalLength?: number;
        thousandsSep?: string;
        decimalSep?: string;
      };
    },
  ) => {
    const dl = options.hash.decimalLength || 2;
    const ts = options.hash.thousandsSep || ',';
    const ds = options.hash.decimalSep || '.';

    const parsedValue = parseFloat(value);

    const re = `\\d(?=(\\d{3})+${dl > 0 ? '\\D' : '$'})`;

    const num = parsedValue.toFixed(Math.max(0, ~~dl));

    return (ds ? num.replace('.', ds) : num).replace(
      new RegExp(re, 'g'),
      `$&${ts}`,
    );
  },
);

const adaptiveNumberFormatter = createSmartNumberFormatter();
Handlebars.registerHelper('adaptiveFormat', value =>
  adaptiveNumberFormatter(value),
);
// DODO added stop 56601134

Helpers.registerHelpers(Handlebars);
