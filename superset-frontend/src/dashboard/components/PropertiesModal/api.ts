/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { SupersetClient } from '@superset-ui/core';
import rison from 'rison';
import {
  RoleWithDetails,
  UserSearchResult,
  Team,
  RolesApiResponse,
  UsersApiResponse,
  TeamsApiResponse,
} from './types';

/**
 * Загружает роли с возможностью поиска
 */
export const loadRoles = async (
  input = '',
  page = 0,
  pageSize = 25,
): Promise<{ data: RoleWithDetails[]; totalCount: number }> => {
  const query = rison.encode({
    filter: input,
    page,
    page_size: pageSize,
  });

  try {
    const response = await SupersetClient.get({
      endpoint: `/api/v1/dashboard/related/roles?q=${query}`,
    });

    const roles = response.json.result
      .filter((item: { extra: { active: boolean } }) =>
        item.extra.active !== undefined ? item.extra.active : true,
      )
      .map((item: { value: number; text: string }) => ({
        id: item.value,
        name: item.text,
      }));

    return {
      data: roles,
      totalCount: response.json.count,
    };
  } catch (error) {
    console.error('Error loading roles:', error);
    return { data: [], totalCount: 0 };
  }
};

/**
 * Получает детальную информацию о роли, включая связанную команду
 */
export const getTeamDetails = async (role: {
  id: number;
  name: string;
}): Promise<RoleWithDetails | null> => {
  try {
    try {
      const teamResponse = await SupersetClient.get({
        endpoint: `/api/v1/team/${2}`,
      });

      return {
        id: role.id,
        name: role.name,
        team: teamResponse.json.result,
      };
    } catch (teamError) {
      // Если не удалось получить информацию о команде, возвращаем только роль
      return {
        id: role.id,
        name: role.name,
      };
    }
  } catch (error) {
    console.error('Error getting role details:', error);
    return null;
  }
};

/**
 * Поиск пользователей по имени или ID с информацией о команде
 */
export const searchUsers = async (
  searchTerm: string,
  page = 0,
  pageSize = 25,
): Promise<{ data: UserSearchResult[]; totalCount: number }> => {
  const query = rison.encode({
    filter: searchTerm,
    page,
    page_size: pageSize,
  });

  try {
    const response = await SupersetClient.get({
      endpoint: `/api/v1/dodo_user/?q=${query}`,
    });

    const users = response.json.result.map((user: any) => ({
      id: user.id,
      username: user.username,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      is_active: user.is_active,
      team_name: user.teams?.name,
      team_id: user.teams?.id,
      country_name: user.user_info?.country_name,
      dodo_role: user.user_info?.dodo_role,
      created_on: user.created_on,
      last_login: user.last_login,
      login_count: user.login_count,
    }));

    return {
      data: users,
      totalCount: response.json.count,
    };
  } catch (error) {
    console.error('Error searching users:', error);
    return { data: [], totalCount: 0 };
  }
};

/**
 * Получает информацию о команде по ID
 */
export const getTeamById = async (teamId: number): Promise<Team | null> => {
  try {
    const response = await SupersetClient.get({
      endpoint: `/api/v1/team/${teamId}`,
    });

    return response.json.result;
  } catch (error) {
    console.error('Error getting team details:', error);
    return null;
  }
};
