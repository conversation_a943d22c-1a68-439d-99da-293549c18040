/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { useState, useEffect, useCallback } from 'react';
import { Input, Tabs, Checkbox, Typography, Tag, Space } from 'antd';
import { SearchOutlined, UserOutlined, TeamOutlined } from '@ant-design/icons';
import { t, styled } from '@superset-ui/core';
import Modal from 'src/components/Modal';
import { loadRoles, getTeamDetails, searchUsers } from './api';
import {
  RoleSelectionModalProps,
  RoleWithDetails,
  UserSearchResult,
} from './types';

const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { Search } = Input;

const StyledModal = styled(Modal)`
  .ant-modal-body {
    padding: 24px;
    max-height: 600px;
    overflow-y: auto;
  }
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-content-holder {
    max-height: 450px;
    overflow-y: auto;
  }
`;

const RoleItem = styled.div`
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.base};
    background-color: ${({ theme }) => theme.colors.grayscale.light4};
  }

  &.selected {
    border-color: ${({ theme }) => theme.colors.primary.base};
    background-color: ${({ theme }) => theme.colors.primary.light4};
  }
`;

const UserItem = styled.div`
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: 6px;
  margin-bottom: 8px;
`;

const RoleSelectionModal: React.FC<RoleSelectionModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  selectedRoles,
  title = t('Select Roles'),
}) => {
  const [activeTab, setActiveTab] = useState('roles');
  const [roleSearchTerm, setRoleSearchTerm] = useState('');
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [roles, setRoles] = useState<RoleWithDetails[]>([]);
  const [users, setUsers] = useState<UserSearchResult[]>([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<Set<number>>(
    new Set(),
  );
  const [loading, setLoading] = useState(false);
  const [roleDetails, setRoleDetails] = useState<Map<string, RoleWithDetails>>(
    new Map(),
  );

  // Инициализация выбранных ролей
  useEffect(() => {
    setSelectedRoleIds(new Set(selectedRoles.map(role => role.id)));
  }, [selectedRoles]);

  // Загрузка ролей
  const loadRolesData = useCallback(async (searchTerm = '') => {
    setLoading(true);
    try {
      const { data } = await loadRoles(searchTerm, 0, 50);
      setRoles(data);
    } catch (error) {
      console.error('Error loading roles:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Поиск пользователей
  const searchUsersData = useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setUsers([]);
      return;
    }

    setLoading(true);
    try {
      const { data } = await searchUsers(searchTerm, 0, 20);
      setUsers(data);
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Получение деталей команды
  const loadTeamDetails = useCallback(
    async (role: RoleWithDetails) => {
      if (roleDetails.has(role.name)) return;

      try {
        const details = await getTeamDetails(role);
        if (details) {
          setRoleDetails(prev => new Map(prev.set(role.name, details)));
        }
      } catch (error) {
        console.error('Error loading team details:', error);
      }
    },
    [roleDetails],
  );

  // Загрузка ролей при открытии модального окна
  useEffect(() => {
    if (visible) {
      loadRolesData();
    }
  }, [visible, loadRolesData]);

  // Поиск ролей
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'roles') {
        loadRolesData(roleSearchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [roleSearchTerm, activeTab, loadRolesData]);

  // Поиск пользователей
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'users') {
        searchUsersData(userSearchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [userSearchTerm, activeTab, searchUsersData]);

  const handleRoleToggle = (role: RoleWithDetails) => {
    const newSelectedIds = new Set(selectedRoleIds);
    if (newSelectedIds.has(role.id)) {
      newSelectedIds.delete(role.id);
    } else {
      newSelectedIds.add(role.id);
      loadTeamDetails(role);
    }
    setSelectedRoleIds(newSelectedIds);
  };

  const handleConfirm = () => {
    const selectedRolesList = roles.filter(role =>
      selectedRoleIds.has(role.id),
    );
    onConfirm(selectedRolesList);
  };

  const renderRoleItem = (role: RoleWithDetails) => {
    const isSelected = selectedRoleIds.has(role.id);
    const details = roleDetails.get(role.name);

    return (
      <RoleItem
        key={role.id}
        className={isSelected ? 'selected' : ''}
        onClick={() => handleRoleToggle(role)}
      >
        <Space align="start">
          <Checkbox checked={isSelected} />
          <div style={{ flex: 1 }}>
            <Title level={5} style={{ margin: 0 }}>
              {role.name}
            </Title>
            {details?.team && (
              <div style={{ marginTop: 8 }}>
                <Tag icon={<TeamOutlined />} color="blue">
                  {t('Team')}: {details.team.name}
                </Tag>
                {details.team.participants.length > 0 && (
                  <Tag icon={<UserOutlined />} color="green">
                    {t('Members')}: {details.team.participants.length}
                  </Tag>
                )}
              </div>
            )}
            {details?.team?.participants &&
              details.team.participants.length > 0 && (
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {t('Team members')}:{' '}
                    {details.team.participants
                      .slice(0, 3)
                      .map(p => `${p.first_name} ${p.last_name}`)
                      .join(', ')}
                    {details.team.participants.length > 3 &&
                      ` +${details.team.participants.length - 3} ${t('more')}`}
                  </Text>
                </div>
              )}
          </div>
        </Space>
      </RoleItem>
    );
  };

  const renderUserItem = (user: UserSearchResult) => (
    <UserItem key={user.id}>
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <div>
          <Title level={5} style={{ margin: 0 }}>
            {user.first_name} {user.last_name} ({user.username})
          </Title>
          <Text type="secondary">{user.email}</Text>
        </div>
        {user.team_name && (
          <Tag icon={<TeamOutlined />} color="blue">
            {t('Team')}: {user.team_name}
          </Tag>
        )}
        {user.dodo_role && (
          <Tag color="orange">
            {t('Role')}: {user.dodo_role}
          </Tag>
        )}
        {user.country_name && (
          <Tag color="purple">
            {t('Country')}: {user.country_name}
          </Tag>
        )}
      </Space>
    </UserItem>
  );

  return (
    <StyledModal
      title={title}
      show={visible}
      onHide={onCancel}
      onHandledPrimaryAction={handleConfirm}
      primaryButtonName={`${t('Confirm')} (${selectedRoleIds.size})`}
      width="800px"
    >
      <StyledTabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('Select Roles')} key="roles">
          <Search
            placeholder={t('Search roles...')}
            value={roleSearchTerm}
            onChange={e => setRoleSearchTerm(e.target.value)}
            style={{ marginBottom: 16 }}
            prefix={<SearchOutlined />}
          />
          <div style={{ maxHeight: 400, overflowY: 'auto' }}>
            {roles.map(renderRoleItem)}
            {roles.length === 0 && !loading && (
              <div style={{ textAlign: 'center', padding: 20 }}>
                <Text type="secondary">{t('No roles found')}</Text>
              </div>
            )}
          </div>
        </TabPane>
        <TabPane tab={t('Search Users')} key="users">
          <Search
            placeholder={t('Search users by name or ID...')}
            value={userSearchTerm}
            onChange={e => setUserSearchTerm(e.target.value)}
            style={{ marginBottom: 16 }}
            prefix={<SearchOutlined />}
          />
          <div style={{ maxHeight: 400, overflowY: 'auto' }}>
            {users.map(renderUserItem)}
            {users.length === 0 && userSearchTerm && !loading && (
              <div style={{ textAlign: 'center', padding: 20 }}>
                <Text type="secondary">{t('No users found')}</Text>
              </div>
            )}
            {!userSearchTerm && (
              <div style={{ textAlign: 'center', padding: 20 }}>
                <Text type="secondary">
                  {t('Enter a name or ID to search for users')}
                </Text>
              </div>
            )}
          </div>
        </TabPane>
      </StyledTabs>
    </StyledModal>
  );
};

export default RoleSelectionModal;
