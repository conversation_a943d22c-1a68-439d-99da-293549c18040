/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Базовый тип роли
export interface Role {
  id: number;
  name: string;
}

// Пользователь с базовой информацией
export interface User {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  created_on?: string;
  last_login?: string;
  login_count?: number;
}

// Команда с участниками и ролями
export interface Team {
  id: number;
  name: string;
  slug: string;
  is_external: boolean;
  roles: Role[];
  participants: User[];
}

// Расширенная информация о роли с деталями команды
export interface RoleWithDetails extends Role {
  team?: Team;
  description?: string;
}

// Результат поиска пользователя с информацией о команде
export interface UserSearchResult extends User {
  team_name?: string;
  team_id?: number;
  country_name?: string;
  dodo_role?: string;
}

// Пропсы для модального окна выбора ролей
export interface RoleSelectionModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (selectedRoles: Role[]) => void;
  selectedRoles: Role[];
  title?: string;
}

// Опции для AsyncSelect
export interface SelectOption {
  value: number;
  label: string;
}

// Результат API запроса для ролей
export interface RolesApiResponse {
  result: RoleWithDetails[];
  count: number;
}

// Результат API запроса для поиска пользователей
export interface UsersApiResponse {
  result: UserSearchResult[];
  count: number;
}

// Результат API запроса для команд
export interface TeamsApiResponse {
  result: Team[];
  count: number;
}
